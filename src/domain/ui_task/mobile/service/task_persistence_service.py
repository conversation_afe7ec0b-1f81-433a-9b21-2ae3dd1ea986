#!/usr/bin/env python3
"""
任务持久化服务 - 封装任务的数据库操作逻辑
"""

from datetime import datetime
from typing import Dict, Any, Optional, Type

from loguru import logger

from src.domain.ui_task.mobile.android.screenshot_manager import screenshot_manager
from src.domain.ui_task.mobile.repo.dao import UITask, UITaskAction
from src.domain.ui_task.mobile.repo.ui_task_repository import UITaskRepository, UITaskActionRepository
from src.schema.action_types import ExecutionStatus, ActionStatus


class TaskPersistenceService:
    """任务持久化服务"""

    def __init__(self):
        self.task_repo = UITaskRepository()
        self.action_repo = UITaskActionRepository()

    def create_task_from_request(self, task_id: str, request: Any, device_id: str) -> UITask:
        """
        从请求创建任务
        
        Args:
            task_id: 任务ID
            request: 测试用例请求
            device_id: 设备ID
            
        Returns:
            创建的任务对象
        """
        try:
            # 准备任务数据
            task_data = {
                "task_id": task_id,
                "task_name": request.task_name,
                "execution_mode": "step_by_step" if request.task_step_by_step else "aggregation",
                "app_id": request.app_id,
                "agent_type": request.agent_type,
                "agent_config_id": getattr(request, 'agent_config_id', None),
                "device_id": device_id,
                "device_type": request.device.type,
                "status": ExecutionStatus.READY,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }

            # 处理步骤信息
            if request.task_step_by_step:
                # 将 TestCaseStep 对象列表转换为可序列化的字典列表
                task_data["task_step_by_step"] = [
                    step.model_dump() if hasattr(step, 'model_dump') else step.dict()
                    for step in request.task_step_by_step
                ]
            else:
                # task_aggregation_step 是字符串类型
                task_data["task_aggregation_step"] = request.task_aggregation_step

            # 处理期望结果
            if hasattr(request, 'task_expect_result') and request.task_expect_result:
                # 处理新格式的期望结果 (ExpectedResult 对象)
                if hasattr(request.task_expect_result, 'model_dump'):
                    task_data["task_expect_result"] = request.task_expect_result.model_dump()
                elif hasattr(request.task_expect_result, 'dict'):
                    task_data["task_expect_result"] = request.task_expect_result.dict()
                else:
                    task_data["task_expect_result"] = request.task_expect_result
            # 创建任务
            task = self.task_repo.create_task(task_data)
            logger.info(f"[{task_id}] ✅ Task created in database: {task_id}")
            return task

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to create task {task_id}: {str(e)}")
            raise

    def get_task_by_task_id(self, task_id: str) -> Optional[UITask]:
        """
        根据task_id获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务对象或None
        """
        return self.task_repo.get_task_by_task_id(task_id)

    def start_task_execution(self, task_id: str) -> bool:
        """
        开始任务执行
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        try:
            success = self.task_repo.start_task_execution(task_id)
            if success:
                logger.info(f"[{task_id}] 🚀 Task execution started: {task_id}")
            else:
                logger.warning(f"[task_id: {task_id}] ⚠️ Failed to start task execution: {task_id}")
            return success
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error starting task execution {task_id}: {str(e)}")
            return False

    def complete_task_execution(self, task_id: str, success: bool = True,
                               error_message: str = None, execution_log: str = None) -> bool:
        """
        完成任务执行

        Args:
            task_id: 任务ID
            success: 是否成功
            error_message: 错误信息
            execution_log: 执行日志（文本格式）

        Returns:
            是否成功
        """
        try:
            result = self.task_repo.complete_task_execution(
                task_id, success, error_message, execution_log
            )

            if result:
                status = "completed" if success else "failed"
                logger.info(f"[{task_id}] ✅ Task execution {status}: {task_id}")
            else:
                logger.warning(f"[{task_id}] ⚠️ Failed to complete task execution: {task_id}")

            return result
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error completing task execution {task_id}: {str(e)}")
            return False

    def update_task_status(self, task_id: str, status: ExecutionStatus, 
                          error_message: str = None) -> bool:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            error_message: 错误信息
            
        Returns:
            是否成功
        """
        try:
            end_time = datetime.now() if status in [ExecutionStatus.SUCCEED, ExecutionStatus.FAILED, ExecutionStatus.TERMINATE] else None
            result = self.task_repo.update_task_status(task_id, status, error_message, end_time)
            
            if result:
                logger.info(f"[{task_id}] 📝 Task status updated: {task_id} -> {status.value}")
            else:
                logger.warning(f"[{task_id}] ⚠️ Failed to update task status: {task_id}")
                
            return result
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error updating task status {task_id}: {str(e)}")
            return False

    def update_task_image_path(self, task_id: str, image_path: str) -> bool:
        """
        更新任务的截图路径（每步执行后都更新，最终保留最后一步的截图）

        Args:
            task_id: 任务ID
            image_path: 截图路径

        Returns:
            是否成功
        """
        try:
            result = self.task_repo.update_task_image_path(task_id, image_path)

            if result:
                logger.info(f"[{task_id}] 📸 Task image path updated: {image_path}")
            else:
                logger.warning(f"[{task_id}] ⚠️ Failed to update task image path: {task_id}")

            return result
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error updating task image path {task_id}: {str(e)}")
            return False

    def append_execution_log(self, task_id: str, log_entry: str) -> bool:
        """
        追加执行日志条目

        Args:
            task_id: 任务ID
            log_entry: 新的日志条目

        Returns:
            是否成功
        """
        try:
            # 获取当前日志
            current_task = self.task_repo.get_task_by_task_id(task_id)
            if not current_task:
                logger.warning(f"[{task_id}] ⚠️ Task not found: {task_id}")
                return False

            current_log = current_task.execution_log or ""

            # 追加新的日志条目
            if current_log:
                updated_log = current_log + "\n" + log_entry
            else:
                updated_log = log_entry

            # 更新日志
            return self.task_repo.update_execution_log(task_id, updated_log)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error appending execution log {task_id}: {str(e)}")
            return False

    def create_task_action(self, task_id: str, step_name: str, action_command: str = None,
                          decision_content: str = None) -> Optional[UITaskAction]:
        """
        创建任务动作

        Args:
            task_id: 任务ID
            step_name: 步骤名称
            action_command: 动作命令
            decision_content: 决策内容

        Returns:
            创建的动作对象或None
        """
        try:
            # 获取任务
            task = self.get_task_by_task_id(task_id)
            if not task:
                logger.error(f"[{task_id}] ❌ Task not found: {task_id}")
                return None

            # 准备动作数据
            action_data = {
                "task_id": task.task_id,  # 使用任务UUID，匹配外键约束
                "step_name": step_name,
                "status": ActionStatus.PROCESSING,
                "start_time": datetime.now(),
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }

            if action_command:
                action_data["action"] = action_command
            if decision_content:
                action_data["decision_content"] = decision_content

            # 创建动作
            action = self.action_repo.create_action(action_data)
            logger.info(f"[{task_id}] ✅ Task action created: {task_id} - {step_name}")
            return action

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to create task action {task_id}: {str(e)}")
            return None

    def complete_task_action(self, action_id: int, success: bool = True,
                           error_message: str = None,
                           before_screenshot: str = None, verification_result: Dict = None,
                           final_action_command: str = None) -> bool:
        """
        完成任务动作

        Args:
            action_id: 动作ID
            success: 是否成功
            error_message: 错误信息
            before_screenshot: 执行前截图路径
            verification_result: 验证结果
            final_action_command: 最终的动作命令（带坐标）

        Returns:
            是否成功
        """
        try:
            result = self.action_repo.complete_action_execution(
                action_id, success, error_message
            )

            # 更新额外信息
            if result:
                self.action_repo.update_action_extra_info(
                    action_id, before_screenshot, verification_result, final_action_command
                )

            if result:
                status = "success" if success else "failed"
                logger.info(f"[action_id: {action_id}] ✅ Task action {status}: {action_id}")
            else:
                logger.warning(f"[action_id: {action_id}] ⚠️ Failed to complete task action: {action_id}")

            return result
        except Exception as e:
            logger.error(f"[action_id: {action_id}] ❌ Error completing task action {action_id}: {str(e)}")
            return False

    def get_task_actions(self, task_id: str) -> list[Any] | list[Type[UITaskAction]]:
        """
        获取任务的所有动作

        Args:
            task_id: 任务ID

        Returns:
            动作列表
        """
        try:
            task = self.get_task_by_task_id(task_id)
            if not task:
                return []

            return self.action_repo.get_actions_by_task_id(task.task_id)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error getting task actions {task_id}: {str(e)}")
            return []

    def delete_task_and_actions(self, task_id: str) -> bool:
        """
        删除任务及其所有动作

        Args:
            task_id: 任务ID

        Returns:
            是否删除成功
        """
        try:
            # 首先获取任务以确保存在
            task = self.get_task_by_task_id(task_id)
            if not task:
                logger.warning(f"[{task_id}] ⚠️ Task not found for deletion: {task_id}")
                return False

            # 删除任务的所有动作
            actions_deleted = self.action_repo.delete_actions_by_task_id(task.task_id)
            if actions_deleted:
                logger.info(f"[{task_id}] ✅ Task actions deleted: {task_id}")

            # 删除任务本身
            task_deleted = self.task_repo.delete_task(task_id)
            if task_deleted:
                logger.info(f"[{task_id}] ✅ Task deleted: {task_id}")

                # 删除任务的截图目录
                screenshots_deleted = screenshot_manager.delete_task_screenshots(task_id)
                if screenshots_deleted:
                    logger.info(f"[{task_id}] ✅ Task screenshots deleted: {task_id}")
                else:
                    logger.warning(f"[{task_id}] ⚠️ Failed to delete task screenshots: {task_id}")

                return True
            else:
                logger.error(f"[{task_id}] ❌ Failed to delete task: {task_id}")
                return False

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error deleting task and actions {task_id}: {str(e)}")
            return False

    def stop_task_execution(self, task_id: str) -> bool:
        """
        停止任务执行，更新任务和所有运行中的动作状态为失败

        Args:
            task_id: 任务ID

        Returns:
            是否停止成功
        """
        try:
            # 更新任务状态为终止，错误信息为用户停止
            task_updated = self.update_task_status(
                task_id=task_id,
                status=ExecutionStatus.TERMINATE,
                error_message="用户停止"
            )

            if not task_updated:
                logger.warning(f"[{task_id}] ⚠️ Failed to update task status for stop: {task_id}")
                return False

            # 获取任务的所有动作
            actions = self.get_task_actions(task_id)

            # 更新所有运行中的动作状态为失败
            stopped_actions = 0
            for action in actions:
                if action.status == ActionStatus.PROCESSING:
                    success = self.action_repo.update_action_status(
                        action_id=action.id.value,
                        status=ActionStatus.FAILED.value,
                        error_message="用户停止",
                    )
                    if success:
                        stopped_actions += 1

            # 追加停止日志
            stop_log = f"\n[{datetime.now().strftime('%H:%M:%S')}] 任务停止 - 用户停止任务"
            self.append_execution_log(task_id, stop_log)

            logger.info(f"[{task_id}] ✅ Task stopped: {task_id}, {stopped_actions} actions updated")
            return True

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error stopping task execution {task_id}: {str(e)}")
            return False


# 全局单例实例
task_persistence_service = TaskPersistenceService()
