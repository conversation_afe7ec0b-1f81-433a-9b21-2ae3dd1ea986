#!/usr/bin/env python3
"""
测试用例API接口
"""

import threading
from datetime import datetime
from typing import Dict, Any
from uuid import uuid4

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import FileResponse
from loguru import logger

from src.api.v1.dto import (
    UITaskCreateRequest, UITaskStopRequest, VideoAnalyzeRequest
)
from src.application.ui_task_application import ui_task_application
from src.schema.action_types import ExecutionStatus

router = APIRouter(tags=["ui-task"])

# 存储正在执行的任务
running_tasks: Dict[str, Dict[str, Any]] = {}

# 存储后台任务
background_tasks_registry: Dict[str, threading.Thread] = {}


def execute_test_case_background(
        task_id: str,
        request: UITaskCreateRequest
):
    """
    后台执行测试用例

    Args:
        task_id: 任务ID
        request: 测试用例执行请求
    """
    try:
        logger.info(f"[{task_id}] 🚀 Background execution started for task: {task_id}")

        # 开始任务执行（更新数据库状态）
        ui_task_application.start_task_execution(task_id)

        # 执行测试用例
        result = ui_task_application.execute_test_case_enhanced(
            task_id=task_id,
            request=request
        )

        # 检查任务是否被停止
        was_stopped = ui_task_application.is_task_stopped(task_id)

        # 更新数据库中的任务状态
        success = result.get("success", False)
        error_message = result.get("error") if not success else None

        # 完成任务执行（更新数据库）
        ui_task_application.complete_task_execution(
            task_id=task_id,
            success=success,
            error_message=error_message
        )

        # 更新内存中的任务状态（保持兼容性）
        if task_id in running_tasks:
            running_tasks[task_id]["status"] = "completed"
            running_tasks[task_id]["end_time"] = datetime.now().isoformat()
            running_tasks[task_id]["result"] = result
            running_tasks[task_id]["success"] = success

            # 保留停止状态信息
            if was_stopped or running_tasks[task_id].get("is_stopped", False):
                running_tasks[task_id]["is_stopped"] = True

        # 注销任务
        ui_task_application.unregister_task(task_id)

        logger.info(f"[{task_id}] ✅ Background execution completed for task: {task_id}")

    except Exception as e:
        logger.error(f"[{task_id}] ❌ Background execution failed for task {task_id}: {str(e)}")

        # 检查任务是否被停止
        was_stopped = ui_task_application.is_task_stopped(task_id)

        # 更新数据库中的任务状态为失败
        ui_task_application.update_task_status(
            task_id=task_id,
            status=ExecutionStatus.FAILED,
            error_message=str(e)
        )

        # 更新内存中的任务状态（保持兼容性）
        if task_id in running_tasks:
            running_tasks[task_id]["status"] = "failed"
            running_tasks[task_id]["end_time"] = datetime.now().isoformat()
            running_tasks[task_id]["error"] = str(e)

            # 保留停止状态信息
            if was_stopped or running_tasks[task_id].get("is_stopped", False):
                running_tasks[task_id]["is_stopped"] = True

        # 注销任务
        ui_task_application.unregister_task(task_id)

    finally:
        # 清理ADB键盘设置（通过Application层）
        try:
            ui_task_application.cleanup_adb_keyboards(task_id)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error cleaning up keyboard: {str(e)}")

        # 清理后台任务注册
        if task_id in background_tasks_registry:
            del background_tasks_registry[task_id]


@router.post("/ui-task/create")
async def create_ui_task(request: UITaskCreateRequest):
    """
    创建执行任务

    Args:
        request: 测试用例执行请求

    Returns:
        任务创建结果，包含task_id
    """
    task_id = uuid4().hex

    try:
        # 解析设备ID - 使用ExecutionService的方法正确提取设备ID
        device_id = request.device.android.url

        # 创建任务到数据库
        ui_task_application.create_task_from_request(
            task_id=task_id,
            request=request,
            device_id=device_id
        )

        # 记录任务开始（保持内存兼容性）
        running_tasks[task_id] = {
            "status": ExecutionStatus.PROCESSING.value,
            "start_time": datetime.now().isoformat(),
            "task_name": request.task_name,
            "verification_mode": "step_by_step" if request.task_step_by_step else "aggregation",
            "app_id": request.app_id,
            "agent_type": request.agent_type,
            "device_type": request.device.type
        }

        # 注册任务到停止管理器
        ui_task_application.register_task(task_id, request.task_name)

        # 设置ADB键盘（通过Application层）
        try:
            keyboard_setup_success = ui_task_application.setup_adb_keyboards(task_id, device_id)
            if keyboard_setup_success:
                logger.info(f"[{task_id}] ✅ ADB keyboards setup successful")
            else:
                logger.warning(f"[{task_id}] ⚠️ ADB keyboards setup failed, continuing with task")
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error setting up ADB keyboards: {str(e)}")

        logger.info(f"[{task_id}] 🚀 Starting test case execution: {request.task_name} (Task ID: {task_id})")

        # 启动后台执行线程
        background_thread = threading.Thread(
            target=execute_test_case_background,
            args=(task_id, request),
            daemon=True
        )

        # 注册后台任务
        background_tasks_registry[task_id] = background_thread

        # 启动线程
        background_thread.start()

        # 立即返回任务ID和启动状态（按照新的响应格式）
        return {
            "code": 0,
            "message": "",
            "data": {
                "task_id": task_id
            }
        }

    except Exception as e:
        # 更新数据库中的任务状态为失败
        ui_task_application.update_task_status(
            task_id=task_id,
            status=ExecutionStatus.ERROR,
            error_message=str(e)
        )

        # 更新内存中的任务状态为失败（保持兼容性）
        if task_id in running_tasks:
            running_tasks[task_id]["status"] = "failed"
            running_tasks[task_id]["error"] = str(e)

        # 注销任务
        ui_task_application.unregister_task(task_id)

        logger.error(f"[{task_id}] ❌ Failed to start test case execution: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to start test case execution: {str(e)}",
            "data": {
                "task_id": ""
            }
        }


@router.get("/ui-task/status/get")
async def get_ui_task_status(task_id: str):
    """
    查询任务状态（执行中，成功，失败）

    Args:
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    try:
        # 首先从数据库获取任务信息
        task = ui_task_application.get_task_by_id(task_id)

        if not task:
            # 如果数据库中没有，检查内存中是否存在（兼容性）
            if task_id not in running_tasks:
                return {
                    "code": 1,
                    "message": f"Task {task_id} not found",
                    "data": {
                        "status": ExecutionStatus.FAILED.value,
                        "message": "Task not found"
                    }
                }

            # 从内存获取状态（向后兼容）
            task_info = running_tasks[task_id]
            memory_status = task_info.get("status", "unknown")

            # 映射内存状态到API状态
            if memory_status == ExecutionStatus.PROCESSING.value:
                api_status = ExecutionStatus.PROCESSING.value
            elif memory_status == ExecutionStatus.SUCCEED.value:
                api_status = ExecutionStatus.SUCCEED.value
            elif memory_status == ExecutionStatus.TERMINATE.value:
                api_status = ExecutionStatus.TERMINATE.value
            else:
                api_status = ExecutionStatus.FAILED.value

            return {
                "code": 0,
                "message": "Success",
                "data": {
                    "status": api_status,
                    "message": task_info.get("error", "Task status retrieved from memory")
                }
            }

        # 从数据库映射状态到API状态
        db_status = task.status.value
        if db_status == ExecutionStatus.PROCESSING.value:
            api_status = ExecutionStatus.PROCESSING.value
        elif db_status == ExecutionStatus.SUCCEED.value:
            api_status = ExecutionStatus.SUCCEED.value
        elif db_status == ExecutionStatus.TERMINATE.value:
            api_status = ExecutionStatus.TERMINATE.value
        else:
            api_status = ExecutionStatus.FAILED.value

        return {
            "code": 0,
            "message": "Success",
            "data": {
                "status": api_status,
                "message": task.error_message or f"Task is {api_status}"
            }
        }

    except Exception as e:
        logger.error(f"[task_id: {task_id}] ❌ Failed to get task status {task_id}: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to get task status: {str(e)}",
            "data": {
                "status": ExecutionStatus.FAILED.value,
                "message": str(e)
            }
        }


@router.get("/ui-task/record/get")
async def get_ui_task_record(task_id: str):
    """
    查询任务执行步骤记录

    Args:
        task_id: 任务ID

    Returns:
        任务执行记录列表
    """
    try:
        # 从数据库获取任务动作记录
        actions = ui_task_application.get_task_actions(task_id)

        if not actions:
            return {
                "code": 0,
                "message": "Success",
                "data": []
            }

        # 转换为API格式
        record_items = []
        for index, action in enumerate(actions, 1):
            execution_cost = 0.0
            if action.start_time and action.end_time:
                execution_cost = (action.end_time - action.start_time).total_seconds()

            record_item = {
                "number": index,
                "step_name": action.step_name or "",
                "thought": action.decision_content or "",
                "cost": execution_cost,
                "image_path": action.image_path,
                "action": action.action or "",
                "status": action.status,
                "start_time": action.start_time.isoformat() if action.start_time else "",
                "end_time": action.end_time.isoformat() if action.end_time else ""
            }
            record_items.append(record_item)

        return {
            "code": 0,
            "message": "Success",
            "data": record_items
        }

    except Exception as e:
        logger.error(f"[task_id: {task_id}] ❌ Failed to get task record {task_id}: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to get task record: {str(e)}",
            "data": []
        }


@router.get("/ui-task/log/get")
async def get_ui_task_log(task_id: str):
    """
    查询任务日志

    Args:
        task_id: 任务ID

    Returns:
        任务日志内容
    """
    try:
        # 从数据库获取任务信息
        task = ui_task_application.get_task_by_id(task_id)

        if not task:
            return {
                "code": 1,
                "message": f"Task {task_id} not found",
                "data": ""
            }

        # 返回执行日志
        log_content = task.execution_log or ""

        return {
            "code": 0,
            "message": "Success",
            "data": log_content
        }

    except Exception as e:
        logger.error(f"[task_id: {task_id}] ❌ Failed to get task log {task_id}: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to get task log: {str(e)}",
            "data": ""
        }


@router.post("/ui-task/stop")
async def stop_ui_task(req: UITaskStopRequest):
    """
    停止正在执行的UI任务

    停止后会：
    1. 停止任务执行线程
    2. 更新任务状态为失败
    3. 更新所有运行中的步骤状态为失败
    4. 错误信息设置为"用户停止"

    Args:
        req: 停止任务请求

    Returns:
        停止操作结果
    """
    task_id = req.task_id

    try:
        logger.info(f"[task_id: {task_id}] 🛑 Stopping UI task: {task_id}")

        # 首先从数据库获取任务信息
        task = ui_task_application.get_task_by_id(task_id)

        if not task:
            # 如果数据库中没有，检查内存中是否存在（兼容性）
            if task_id not in running_tasks:
                logger.warning(f"[task_id: {task_id}] ⚠️ Task not found: {task_id}")
                return {
                    "code": 1,
                    "message": f"Task {task_id} not found",
                    "data": {
                        "success": False,
                        "task_id": task_id
                    }
                }

        # 1. 使用task_stop_manager停止执行线程
        thread_stopped = ui_task_application.stop_task_thread(task_id)

        # 2. 更新数据库中的任务和动作状态
        db_updated = ui_task_application.stop_task_execution(task_id)

        # 3. 更新内存中的任务状态
        if task_id in running_tasks:
            running_tasks[task_id]["status"] = ExecutionStatus.TERMINATE.value
            running_tasks[task_id]["error_message"] = "用户停止"
            running_tasks[task_id]["end_time"] = datetime.now().isoformat()

        # 4. 清理ADB键盘设置（通过Application层）
        try:
            ui_task_application.cleanup_adb_keyboards(task_id)
        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Error cleaning up keyboards: {str(e)}")

        # 5. 清理后台线程注册
        if task_id in background_tasks_registry:
            del background_tasks_registry[task_id]
            logger.info(f"[task_id: {task_id}] 🧹 Background thread registry cleaned")

        if thread_stopped and db_updated:
            logger.info(f"[task_id: {task_id}] ✅ Task stopped successfully: {task_id}")
            return {
                "code": 0,
                "message": "Task stopped successfully",
                "data": {
                    "success": True,
                    "task_id": task_id,
                    "message": "Task execution stopped and status updated to failed with error: 用户停止"
                }
            }
        else:
            logger.warning(
                f"[task_id: {task_id}] ⚠️ Partial stop success - thread_stopped: {thread_stopped}, db_updated: {db_updated}")
            return {
                "code": 0,  # 仍然返回成功，因为至少部分操作成功了
                "message": "Task stop partially successful",
                "data": {
                    "success": True,
                    "task_id": task_id,
                    "message": f"Thread stopped: {thread_stopped}, Database updated: {db_updated}"
                }
            }

    except Exception as e:
        logger.error(f"[task_id: {task_id}] ❌ Failed to stop task {task_id}: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to stop task: {str(e)}",
            "data": {
                "success": False,
                "task_id": task_id
            }
        }


@router.delete("/ui-task/delete")
async def delete_ui_task(task_id: str):
    """
    删除UI任务及其所有相关数据

    删除操作包括：
    1. 删除任务记录
    2. 删除所有相关的动作记录
    3. 清理内存中的任务状态
    4. 清理后台线程注册

    Args:
        task_id: 任务ID (作为查询参数)

    Returns:
        删除操作结果
    """
    try:
        logger.info(f"[task_id: {task_id}] 🗑️ Deleting UI task: {task_id}")

        # 1. 检查任务是否存在
        task = ui_task_application.get_task_by_id(task_id)
        if not task:
            logger.warning(f"[task_id: {task_id}] ⚠️ Task not found for deletion: {task_id}")
            return {
                "code": 1,
                "message": f"Task {task_id} not found",
                "data": {
                    "success": False,
                    "task_id": task_id
                }
            }

        # 2. 如果任务正在运行，先停止它
        if task_id in running_tasks and running_tasks[task_id].get("status") in ["running", "ready"]:
            logger.info(f"[task_id: {task_id}] 🛑 Task is running, stopping before deletion")

            # 停止执行线程
            ui_task_application.stop_task_thread(task_id)

            # 更新状态为停止
            ui_task_application.stop_task_execution(task_id)

        # 3. 删除数据库中的任务和动作记录
        deleted = ui_task_application.delete_task(task_id)

        if not deleted:
            logger.error(f"[task_id: {task_id}] ❌ Failed to delete task from database: {task_id}")
            return {
                "code": 1,
                "message": f"Failed to delete task {task_id} from database",
                "data": {
                    "success": False,
                    "task_id": task_id
                }
            }

        # 4. 清理内存中的任务状态
        if task_id in running_tasks:
            del running_tasks[task_id]
            logger.info(f"[task_id: {task_id}] 🧹 Memory task state cleaned")

        # 5. 清理ADB键盘设置（通过Application层）
        try:
            ui_task_application.cleanup_adb_keyboards(task_id)
        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Error cleaning up keyboards: {str(e)}")

        # 6. 清理视频文件
        try:
            ui_task_application.delete_task_video(task_id)
            logger.info(f"[task_id: {task_id}] 🧹 Task video cleaned")
        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Error cleaning up video: {str(e)}")

        # 7. 清理后台线程注册
        if task_id in background_tasks_registry:
            del background_tasks_registry[task_id]
            logger.info(f"[task_id: {task_id}] 🧹 Background thread registry cleaned")

        # 8. 从停止管理器中注销任务
        ui_task_application.unregister_task(task_id)
        logger.info(f"[task_id: {task_id}] 🧹 Task unregistered from stop manager")

        logger.info(f"[task_id: {task_id}] ✅ Task deleted successfully: {task_id}")
        return {
            "code": 0,
            "message": "Task deleted successfully",
            "data": {
                "success": True,
                "task_id": task_id,
                "message": "Task and all related data have been deleted"
            }
        }

    except Exception as e:
        logger.error(f"[task_id: {task_id}] ❌ Failed to delete task {task_id}: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to delete task: {str(e)}",
            "data": {
                "success": False,
                "task_id": task_id
            }
        }


@router.get("/ui-task/screenshot")
async def get_task_screenshot(task_id: str, image_path: str):
    """
    获取任务的截图文件

    根据任务ID和截图路径返回图片文件内容

    Args:
        task_id: 任务ID
        image_path: 截图的相对路径

    Returns:
        图片文件响应
    """
    try:
        logger.info(f"[task_id: {task_id}] 🖼️ Requesting screenshot: {image_path}")

        # 验证任务是否存在
        task = ui_task_application.get_task_by_id(task_id)
        if not task:
            logger.warning(f"[task_id: {task_id}] Task not found: {task_id}")
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

        # 验证截图文件是否存在
        if not ui_task_application.screenshot_exists(image_path):
            logger.warning(f"[task_id: {task_id}] Screenshot not found: {image_path}")
            raise HTTPException(status_code=404, detail=f"Screenshot {image_path} not found")

        # 获取截图的完整路径
        full_path = ui_task_application.get_screenshot_full_path(image_path)

        logger.info(f"[task_id: {task_id}] Serving screenshot: {image_path}")

        # 返回文件响应
        return FileResponse(
            path=str(full_path),
            media_type="image/png",
            filename=f"{task_id}_{full_path.name}"
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"[task_id: {task_id}] ❌ Failed to serve screenshot {image_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to serve screenshot: {str(e)}")


@router.post("/ui-task/video/stream/upload")
async def upload_video_stream(
        request: Request,
        expected_size: int = None
):
    """
    流式上传视频文件

    前端一次性发送整个视频流，后端持续接收直到传输完成
    自动生成video_id，按日期存储视频文件

    Args:
        request: HTTP请求对象，包含视频流数据
        expected_size: 预期文件大小（字节，可选，用于进度计算）

    Returns:
        上传结果，包含video_id
    """
    try:
        logger.info(f"🎬 Starting streaming video upload, expected_size: {expected_size}")

        # 检查Content-Type
        content_type = request.headers.get("content-type", "")
        if content_type and "application/octet-stream" not in content_type:
            logger.warning(f"Recommended Content-Type: application/octet-stream, got: {content_type}")

        # 开始流式上传会话
        video_id, error = ui_task_application.start_video_stream(expected_size)
        if not video_id:
            logger.error(f"Failed to start video stream: {error}")
            return {
                "code": 1,
                "message": error or "Failed to start video stream",
                "data": {
                    "success": False
                }
            }

        logger.info(f"✅ Stream session created: {video_id}")

        # 流式读取请求体数据
        total_size = 0
        chunk_count = 0

        async for chunk in request.stream():
            if not chunk:
                break

            chunk_count += 1
            chunk_size = len(chunk)
            total_size += chunk_size

            # 追加数据块到文件
            success, error = await ui_task_application.append_video_chunk(
                video_id, chunk, chunk_count
            )

            if not success:
                logger.error(f"[video_id: {video_id}] ❌ Failed to append stream chunk {chunk_count}: {error}")
                # 取消上传会话
                ui_task_application.cancel_video_stream(video_id)
                return {
                    "code": 1,
                    "message": error or "Failed to append stream chunk",
                    "data": {
                        "video_id": video_id,
                    }
                }

            # 每100个chunk记录一次进度
            if chunk_count % 100 == 0:
                logger.debug(f"[video_id: {video_id}] 📦 Processed {chunk_count} chunks, {total_size} bytes")

        logger.info(f"[video_id: {video_id}] 📥 Stream upload completed: {chunk_count} chunks, {total_size} bytes")

        # 完成流式上传
        final_path, error = ui_task_application.complete_video_stream(video_id)
        if not final_path:
            logger.error(f"[video_id: {video_id}] ❌ Failed to complete video stream: {error}")
            return {
                "code": 1,
                "message": error or "Failed to complete video stream",
                "data": {
                    "success": False,
                    "video_id": video_id
                }
            }

        logger.info(f"[video_id: {video_id}] ✅ Video stream upload completed: {final_path}")
        return {
            "code": 0,
            "message": "Video stream uploaded successfully",
            "data": {
                "video_id": video_id,
            }
        }

    except Exception as e:
        logger.error(f"❌ Failed to upload video stream: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to upload video stream: {str(e)}",
            "data": {}
        }


@router.post("/ui-task/video/analyze")
async def analyze_video_with_ai(req: VideoAnalyzeRequest):
    """
    使用AI分析视频并生成测试用例步骤

    根据video_id找到对应的视频文件，使用豆包AI分析视频内容，
    根据verification_mode生成对应格式的测试用例步骤和期望结果
    
    Returns:
        AI分析结果，格式根据verification_mode而定
    """
    try:
        # 验证verification_mode参数
        if req.verification_mode not in ["step_by_step", "aggregation"]:
            return {
                "code": 1,
                "message": "Invalid verification_mode. Must be 'step_by_step' or 'aggregation'",
                "data": {
                    "video_id": req.video_id
                }
            }

        logger.info(f"[video_id: {req.video_id}] 🤖 Starting AI video analysis with mode: {req.verification_mode}")

        # 调用应用层服务
        analysis_result, error = ui_task_application.analyze_video_with_ai(req.video_id, req.verification_mode)

        if not analysis_result:
            logger.error(f"[video_id: {req.video_id}] ❌ AI analysis failed: {error}")
            return {
                "code": 1,
                "message": error or "Failed to analyze video with AI",
                "data": {
                    "video_id": req.video_id,
                }
            }

        logger.info(f"[video_id: {req.video_id}] ✅ AI video analysis completed successfully")
        return {
            "code": 0,
            "message": "Video analysis completed successfully",
            "data": {
                "analysis_result": analysis_result,
            }
        }

    except Exception as e:
        logger.error(f"[video_id: {req.video_id}] ❌ Failed to analyze video: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to analyze video: {str(e)}",
            "data": {
                "video_id": req.video_id,
            }
        }
