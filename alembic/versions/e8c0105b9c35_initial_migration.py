"""Initial migration

Revision ID: e8c0105b9c35
Revises: beeeda06c8b5
Create Date: 2025-08-08 10:04:24.205015

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e8c0105b9c35'
down_revision: Union[str, Sequence[str], None] = 'beeeda06c8b5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task_action', 'status',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=32),
               type_=sa.Enum('PROCESSING', 'SUCCEED', 'FAILED', name='actionstatus'),
               nullable=False,
               existing_comment='执行状态: processing/succeed/failed')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task_action', 'status',
               existing_type=sa.Enum('PROCESSING', 'SUCCEED', 'FAILED', name='actionstatus'),
               type_=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=32),
               nullable=True,
               existing_comment='执行状态: processing/succeed/failed')
    # ### end Alembic commands ###
