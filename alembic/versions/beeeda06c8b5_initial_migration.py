"""Initial migration

Revision ID: beeeda06c8b5
Revises: b31aa2579c44
Create Date: 2025-08-08 09:56:05.324383

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'beeeda06c8b5'
down_revision: Union[str, Sequence[str], None] = 'b31aa2579c44'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task', 'status',
               existing_type=mysql.ENUM('READY', 'RUNNING', 'COMPLETED', 'FAILED', 'ERROR', 'PAUSED', charset='utf8mb4', collation='utf8mb4_general_ci'),
               type_=mysql.ENUM('PROCESSING', 'SUCCEED', 'FAILED', 'TERMINATE', charset='utf8mb4', collation='utf8mb4_general_ci'),
               comment='任务状态: processing/succeed/failed/terminate',
               existing_comment='任务状态: processing/succeed/failed',
               existing_nullable=False)
    op.alter_column('ui_task_action', 'image_path',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=512),
               comment='执行前截图地址',
               existing_comment='执行后截图地址',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ui_task_action', 'image_path',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=512),
               comment='执行后截图地址',
               existing_comment='执行前截图地址',
               existing_nullable=True)
    op.alter_column('ui_task', 'status',
               existing_type=mysql.ENUM('READY', 'RUNNING', 'COMPLETED', 'FAILED', 'ERROR', 'PAUSED', charset='utf8mb4', collation='utf8mb4_general_ci'),
               comment='任务状态: processing/succeed/failed',
               existing_comment='任务状态: processing/succeed/failed/terminate',
               existing_nullable=False)
    # ### end Alembic commands ###
